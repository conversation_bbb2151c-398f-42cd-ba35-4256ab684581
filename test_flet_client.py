#!/usr/bin/env python3
"""
木偶AI翻唱 Flet客户端 - 测试版本
"""

import flet as ft
import os

def main(page: ft.Page):
    """测试主函数"""
    page.title = "木偶AI翻唱 - 测试版"
    page.theme_mode = ft.ThemeMode.DARK
    page.window_width = 1024
    page.window_height = 768
    
    # 颜色主题
    primary_color = ft.colors.BLUE_400
    surface_color = ft.colors.GREY_800
    background_color = ft.colors.GREY_900
    
    # 创建简单的测试界面
    header = ft.Container(
        content=ft.Row([
            ft.Container(
                content=ft.Column([
                    ft.Text("木偶AI", size=24, weight=ft.FontWeight.BOLD, color=primary_color),
                    ft.Text("AI翻唱 - 测试版", size=16, color=ft.colors.WHITE70)
                ], spacing=0),
                padding=20
            )
        ]),
        bgcolor=surface_color,
        border_radius=10,
        margin=ft.margin.only(bottom=10)
    )
    
    # 测试按钮
    def test_click(e):
        page.add(ft.Text("测试成功！Flet客户端运行正常", color=ft.colors.GREEN))
        page.update()
    
    test_button = ft.ElevatedButton(
        text="测试Flet客户端",
        on_click=test_click,
        bgcolor=primary_color,
        color=ft.colors.WHITE,
        width=200,
        height=50
    )
    
    # 状态信息
    status_info = ft.Column([
        ft.Text("✓ Flet框架已加载", color=ft.colors.GREEN),
        ft.Text("✓ 界面渲染正常", color=ft.colors.GREEN),
        ft.Text("✓ 事件处理正常", color=ft.colors.GREEN),
        ft.Text("", height=20),
        ft.Text("如果看到此界面，说明Flet客户端基础功能正常", color=ft.colors.WHITE70),
        ft.Text("可以运行完整版本: python flet_client.py", color=ft.colors.BLUE_300)
    ], spacing=10)
    
    # 布局
    main_content = ft.Column([
        header,
        ft.Container(
            content=ft.Column([
                test_button,
                ft.Divider(height=20, color=ft.colors.TRANSPARENT),
                status_info
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            bgcolor=surface_color,
            padding=30,
            border_radius=10,
            alignment=ft.alignment.center
        )
    ], spacing=20)
    
    page.add(
        ft.Container(
            content=main_content,
            padding=20,
            bgcolor=background_color,
            expand=True
        )
    )

if __name__ == "__main__":
    print("启动Flet测试客户端...")
    ft.app(target=main)
