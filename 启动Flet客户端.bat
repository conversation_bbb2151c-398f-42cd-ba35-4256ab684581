@echo off
chcp 65001 >nul
title 木偶AI翻唱 Flet客户端

echo ================================================
echo 木偶AI翻唱 Flet客户端
echo ================================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请确保已安装Python并添加到PATH环境变量
    pause
    exit /b 1
)

echo 正在检查依赖...
python -c "import flet" >nul 2>&1
if errorlevel 1 (
    echo 正在安装Flet依赖...
    pip install -r requirements_flet.txt
    if errorlevel 1 (
        echo 依赖安装失败，请手动安装: pip install -r requirements_flet.txt
        pause
        exit /b 1
    )
)

echo 正在启动Flet客户端...
echo.
python flet_client.py

if errorlevel 1 (
    echo.
    echo 客户端启动失败，请检查错误信息
    pause
)
